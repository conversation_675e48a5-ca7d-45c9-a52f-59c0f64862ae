<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>DPR Custom Print Format</title>
    <link rel="stylesheet" href="dpr_custom_styles.css">
</head>
<body>
    <!-- Header -->
    <div class="header">
        YOGI BUILDCON DAILY PROGRESS REPORT
    </div>
    
    <!-- Basic Information -->
    <div class="info-section">
        <div class="info-row">
            <span class="info-label">Client Name :</span>
            <span>{{ doc.client }}</span>
            <span class="date-info">Date : {{ doc.date.strftime('%d-%m-%Y') if doc.date else '' }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">Project :</span>
            <span>{{ doc.project }}</span>
            <span class="date-info">Total Days : {{ doc.total_days }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">Address :</span>
            <span>{{ doc.address }}</span>
            <span class="date-info">Days Elapsed : {{ doc.days_elapsed }}</span>
        </div>
        <div class="info-row">
            <span class="info-label"> </span>
            <span class="date-info">Balance : {{ doc.balance }}</span>
        </div>
    </div>
    
    <!-- Manpower Strength Report -->
    <div class="section-title">Manpower strength report</div>
    <table class="manpower-table">
        <thead>
            <tr>
                <th>No</th>
                <th>Description</th>
                <th>Prev</th>
                <th>Today</th>
                <th>Specialized</th>
                <th>Carp</th>
                <th>Fitt</th>
                <th>B. Mas</th>
                <th>P.Mas</th>
                <th>M/C</th>
            </tr>
        </thead>
        <tbody>
            {% for row in doc.manpower_table %}
            <tr>
                <td>{{ loop.index }}</td>
                <td class="text-left">{{ row.description or '' }}</td>
                <td>{{ row.prev or 0 }}</td>
                <td>{{ row.today or 0 }}</td>
                <td>{{ row.specialized or 0 }}</td>
                <td>{{ row.carp or 0 }}</td>
                <td>{{ row.fitt or 0 }}</td>
                <td>{{ row.b_mas or 0 }}</td>
                <td>{{ row.pmas or 0 }}</td>
                <td>{{ row.mc or 0 }}</td>
            </tr>
            {% endfor %}
            <tr class="total-row">
                <td colspan="2">Total</td>
                <td>{{ doc.total_prev or 0 }}</td>
                <td>{{ doc.total_today or 0 }}</td>
                <td>{{ doc.total_spec or 0 }}</td>
                <td>{{ doc.total_carp or 0 }}</td>
                <td>{{ doc.total_fitt or 0 }}</td>
                <td>{{ doc.total_b_mas or 0 }}</td>
                <td>{{ doc.total_p_mas or 0 }}</td>
                <td>{{ doc.total_mc or 0 }}</td>
            </tr>
        </tbody>
    </table>
    
    <!-- Work Status -->
    <div class="section-title">Work Status</div>
    <div class="work-status-container">
        <div class="work-status-left">
            <table class="work-status-table">
                <thead>
                    <tr>
                        <th>No</th>
                        <th>Work Done Today</th>
                    </tr>
                </thead>
                <tbody>
                    {% for row in doc.wdone_today_table %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td class="text-left">{{ row.work_done_today or '' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        <div class="work-status-right">
            <table class="work-status-table">
                <thead>
                    <tr>
                        <th>No</th>
                        <th>Work Planned Tomorrow</th>
                    </tr>
                </thead>
                <tbody>
                    {% for row in doc.wplanned_tomm_table %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td class="text-left">{{ row.work_planned_tommorow or '' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- Hold Ups / Stoppages -->
    <div class="hold-ups-section">
        <h4>Hold Ups /Stoppages of works with reasons</h4>
        <div class="content-box">{{ doc.hold_ups_stoppages_of_works_with_reasons or 'None' }}</div>
    </div>
    
    <!-- Request for Information -->
    <div class="request-info-section">
        <h4>Request for information</h4>
        <div class="content-box">{{ doc.request_for_information or 'None' }}</div>
    </div>
    
    <!-- Equipment and Material Section -->
    <div class="equipment-material-container">
        <div class="equipment-section">
            <h4 class="subsection-title">Equipment's/Machinery Deployed</h4>
            <table class="equipment-table">
                <thead>
                    <tr>
                        <th>No</th>
                        <th>Machinery Deployed</th>
                        <th>No(s)</th>
                    </tr>
                </thead>
                <tbody>
                    {% for row in doc.mach_deployed_table %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td class="text-left">{{ row.machinery_deployed or '' }}</td>
                        <td>{{ row.nos or '' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <div class="material-section">
            <h4 class="subsection-title">Today's Material Receipt</h4>
            <table class="material-table">
                <thead>
                    <tr>
                        <th>No</th>
                        <th>Material</th>
                        <th>Qty</th>
                        <th>Desc</th>
                        <th>Remark</th>
                    </tr>
                </thead>
                <tbody>
                    {% for row in doc.mat_recp_table %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td class="text-left">{{ row.material or '' }}</td>
                        <td>{{ row.qty or '' }}</td>
                        <td class="text-left">{{ row.desc or '' }}</td>
                        <td class="text-left">{{ row.remark or '' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- Status of Material -->
    <div class="material-status-section">
        <h4>Status of Material :</h4>
        <div class="material-status-content">
            <div class="material-category">
                <strong>Steel</strong>
                <div class="material-items">
                    {{ doc.status_of_material or '' }}
                </div>
            </div>
        </div>
    </div>
    
</body>
</html>