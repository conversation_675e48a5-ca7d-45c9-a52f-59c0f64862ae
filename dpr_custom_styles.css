/* DPR Custom Print Format Styles */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    font-size: 12px;
    line-height: 1.4;
    color: #000;
    background: #fff;
    padding: 2px;
}

/* Header Styles */
.header {
    text-align: center;
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 20px;
    text-decoration: underline;
    letter-spacing: 1px;
}

/* Information Section */
.info-section {
    margin-bottom: 20px;
}

.info-row {
    display: flex;
    margin-bottom: 5px;
    align-items: center;
}

.info-label {
    font-weight: bold;
    width: 120px;
    flex-shrink: 0;
}

.date-info {
    margin-left: auto;
    font-weight: bold;
}

/* Section Titles */
.section-title {
    font-weight: bold;
    text-align: center;
    margin: 20px 0 10px 0;
    text-decoration: underline;
    font-size: 14px;
}

.subsection-title {
    font-weight: bold;
    text-decoration: underline;
    text-align: center;
    margin-bottom: 10px;
    font-size: 12px;
}

/* Table Styles */
table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    font-size: 11px;
}

th, td {
    border: 1px solid #000;
    padding: 4px 6px;
    text-align: center;
    vertical-align: middle;
}

th {
    background-color: #f0f0f0;
    font-weight: bold;
    font-size: 10px;
}

.text-left {
    text-align: left !important;
}

/* Manpower Table */
.manpower-table th:first-child,
.manpower-table td:first-child {
    width: 5%;
}

.manpower-table th:nth-child(2),
.manpower-table td:nth-child(2) {
    width: 25%;
}

.manpower-table th:nth-child(n+3),
.manpower-table td:nth-child(n+3) {
    width: 8.75%;
}

.total-row {
    font-weight: bold;
    background-color: #f8f8f8;
}

/* Work Status */
.work-status-container {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.work-status-left,
.work-status-right {
    flex: 1;
}

.work-status-table {
    width: 100%;
}

.work-status-table th:first-child,
.work-status-table td:first-child {
    width: 10%;
}

.work-status-table th:nth-child(2),
.work-status-table td:nth-child(2) {
    width: 90%;
}

/* Hold Ups and Request Sections */
.hold-ups-section,
.request-info-section {
    margin: 20px 0;
}

.hold-ups-section h4,
.request-info-section h4 {
    font-weight: bold;
    text-decoration: underline;
    text-align: center;
    margin-bottom: 10px;
    font-size: 12px;
}

.content-box {
    min-height: 30px;
    padding: 5px;
    border: 1px solid #ccc;
    background-color: #fafafa;
}

/* Equipment and Material Container */
.equipment-material-container {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.equipment-section,
.material-section {
    flex: 1;
}

/* Equipment Table */
.equipment-table th:first-child,
.equipment-table td:first-child {
    width: 10%;
}

.equipment-table th:nth-child(2),
.equipment-table td:nth-child(2) {
    width: 70%;
}

.equipment-table th:nth-child(3),
.equipment-table td:nth-child(3) {
    width: 20%;
}

/* Material Table */
.material-table th:first-child,
.material-table td:first-child {
    width: 8%;
}

.material-table th:nth-child(2),
.material-table td:nth-child(2) {
    width: 30%;
}

.material-table th:nth-child(3),
.material-table td:nth-child(3) {
    width: 12%;
}

.material-table th:nth-child(4),
.material-table td:nth-child(4) {
    width: 25%;
}

.material-table th:nth-child(5),
.material-table td:nth-child(5) {
    width: 25%;
}

/* Material Status Section */
.material-status-section {
    margin-top: 20px;
}

.material-status-section h4 {
    font-weight: bold;
    text-decoration: underline;
    margin-bottom: 10px;
    font-size: 12px;
}

.material-status-content {
    padding: 10px;
    border: 1px solid #ccc;
    background-color: #fafafa;
}

.material-category {
    margin-bottom: 10px;
}

.material-category strong {
    display: block;
    margin-bottom: 5px;
    text-decoration: underline;
}

.material-items {
    margin-left: 10px;
    line-height: 1.6;
}

/* Print Specific Styles */
@media print {
    body {
        padding: 2px;
        font-size: 11px;
    }
    
    .header {
        font-size: 14px;
    }
    
    table {
        font-size: 10px;
    }
    
    th, td {
        padding: 3px 4px;
    }
    
    .work-status-container,
    .equipment-material-container {
        gap: 15px;
    }
}

/* Responsive Design for smaller screens */
@media screen and (max-width: 768px) {
    .work-status-container,
    .equipment-material-container {
        flex-direction: column;
        gap: 10px;
    }
    
    .info-row {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .date-info {
        margin-left: 0;
        margin-top: 5px;
    }
}

/* Utility Classes */
.no-border {
    border: none !important;
}

.bold {
    font-weight: bold;
}

.center {
    text-align: center;
}

.underline {
    text-decoration: underline;
}

.small-text {
    font-size: 10px;
}

.large-text {
    font-size: 14px;
}
